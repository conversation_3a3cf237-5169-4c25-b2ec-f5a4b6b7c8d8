import { useMemo } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { KycVerificationSearchResponse } from "@/api/data-contracts";
import { CopyButton } from "@/ui-components/copy-button";
import { KycStatusBadge } from "./kyc-status-badge";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";
import { Button } from "@radix-ui/themes";
import { AppMenu, type AppMenuItemProps } from "@repo/ui/app-menu";
import { MediaButton } from "./media-dialog";
import type { KycSubmission } from "./kyc-review/context";

const columnHelper = createColumnHelper<KycVerificationSearchResponse>();

interface UseKycColumnsProps {
  onKycReview?: (value: KycSubmission) => void;
}

export const useKycColumns = ({ onKycReview }: UseKycColumnsProps = {}) => {
  return useMemo(
    () => [
      columnHelper.accessor("publicId", {
        size: 260,
        header: "Portfolio ID",
        cell: ({ getValue }) => {
          const value = getValue();
          return (
            <div className="flex gap-2 justify-between">
              <span>{value}</span>
              {value && <CopyButton text={value} />}
            </div>
          );
        },
      }),
      columnHelper.accessor("email", {
        size: 300,
        header: "Email",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("status", {
        size: 140,
        header: "Status",
        cell: ({ getValue }) => {
          const status = getValue();
          return <KycStatusBadge status={status} />;
        },
      }),
      columnHelper.accessor("firstName", {
        size: 200,
        header: "First name",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("lastName", {
        size: 200,
        header: "Last name",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("nationality", {
        size: 200,
        header: "Nationality",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("countryOfResidence", {
        size: 200,
        header: "Country of residence",
        cell: ({ getValue }) => {
          const value = getValue();
          return <span>{value}</span>;
        },
      }),
      columnHelper.accessor("identityDocumentType", {
        size: 200,
        header: "ID type",
        cell: ({ getValue }) => {
          const value = getValue();
          const displayValue = value
            ?.replace(/_/g, " ")
            .toLowerCase()
            .replace(/\b\w/g, (l) => l.toUpperCase());
          return <span>{displayValue}</span>;
        },
      }),
      columnHelper.accessor("documentFrontKey", {
        size: 240,
        header: "Uploaded documents",
        cell: ({ row }) => {
          const documentFrontKey = row.original.documentFrontKey;
          const documentBackKey = row.original.documentBackKey;
          if (!documentFrontKey && !documentBackKey) return "-";
          return (
            <div className="flex gap-2">
              {documentFrontKey && (
                <MediaButton
                  title="Front side"
                  url={documentFrontKey}
                  type="image"
                />
              )}
              {documentBackKey && (
                <MediaButton
                  title="Back side"
                  url={documentBackKey}
                  type="image"
                />
              )}
            </div>
          );
        },
      }),

      columnHelper.accessor("selfieKey", {
        size: 200,
        header: "Selfie image",
        cell: ({ getValue }) => {
          const value = getValue();
          if (!value) return "-";
          return <MediaButton title="Selfie image" url={value} type="image" />;
        },
      }),
      columnHelper.accessor("submissionDate", {
        size: 200,
        header: "Submitted date",
        cell: ({ getValue }) => {
          const value = getValue();
          if (!value) return "-";
          const date = new Date(value);
          return <span>{date.toLocaleDateString()}</span>;
        },
      }),
      columnHelper.accessor("operatorName", {
        size: 300,
        header: "Rejection reason",
        cell: ({ row }) => {
          const status = row.original.status;
          const actionReason = row.original.actionReason;

          if (status === "REJECTED" || status === "RESTRICTED") {
            const displayReason = actionReason
              ?.replace(/_/g, " ")
              .toLowerCase()
              .replace(/\b\w/g, (l) => l.toUpperCase());
            return <span>{displayReason || "-"}</span>;
          }
          return "-";
        },
      }),
      columnHelper.display({
        size: 80,
        id: "action",
        header: "Action",
        cell: ({ row }) => {
          const submissionId = row.original.submissionId;
          const status = row.original.status;

          const menuConfig: AppMenuItemProps[] = [];

          if (status === "PENDING" && submissionId && onKycReview) {
            menuConfig.push(
              {
                key: "APPROVE",
                label: "Approve",
                onClick: () => {
                  onKycReview({ submissionId, action: "APPROVE" });
                },
              },
              {
                key: "REJECT",
                label: "Reject",
                color: "red",
                onClick: () => {
                  onKycReview({ submissionId, action: "REJECT" });
                },
              },
              {
                key: "RESTRICT",
                label: "Restrict",
                color: "red",
                onClick: () => {
                  onKycReview({ submissionId, action: "RESTRICT" });
                },
              },
            );
          }

          if (menuConfig.length === 0) {
            return (
              <div className="flex justify-center">
                <Button variant="ghost" m="auto" color="gray" size="1" disabled>
                  <DotsHorizontalIcon />
                </Button>
              </div>
            );
          }

          return (
            <div className="flex justify-center">
              <AppMenu config={menuConfig}>
                <Button variant="ghost" m="auto" color="gray" size="1">
                  <DotsHorizontalIcon />
                </Button>
              </AppMenu>
            </div>
          );
        },
      }),
    ],
    [onKycReview],
  );
};
