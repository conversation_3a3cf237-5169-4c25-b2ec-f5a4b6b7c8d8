import { Text, Table, Separator, Button } from "@radix-ui/themes";
import { CopyButton } from "@/ui-components/copy-button";
import { DepositStatusBadge } from "./deposit-status-badge";
import { InfoLayout } from "@repo/ui/info-layout";
import { MediaButton } from "../../kyc-verification/_components/media-dialog";
import type { DepositSubmission } from "./deposit-review";
import type { DepositRecord } from "../types";

interface DetailRowProps {
  label: string;
  value: string | number | React.ReactNode;
  enableCopy?: boolean;
  copyText?: string;
}

const DetailRow = ({ label, value, enableCopy, copyText }: DetailRowProps) => {
  return (
    <div className="flex flex-col gap-1 items-start">
      <Text size="2" color="gray">
        {label}
      </Text>
      <div className="flex gap-2 items-center min-w-0 flex-1 justify-end">
        <Text size="2">{value}</Text>
        {enableCopy && copyText && <CopyButton text={copyText} />}
      </div>
    </div>
  );
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "-";
  try {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  } catch {
    return dateString;
  }
};

export const DepositDetailsHeader = ({
  data,
  onDepositReview,
}: {
  data: DepositRecord;
  onDepositReview: (submission: DepositSubmission | null) => void;
}) => {
  const { depositId, transactionId, userPublicId, status } = data;

  if (!depositId) {
    return null;
  }

  const isPending = status === "PENDING";

  const handleReject = () => {
    onDepositReview({ depositId, action: "REJECT" });
  };

  const handleApprove = () => {
    onDepositReview({ depositId, action: "APPROVE" });
  };

  return (
    <div className="flex gap-6 justify-between">
      <div>
        <div className="grow flex items-center gap-2 mb-1">
          <Text size="4" weight="bold">
            Transaction ID: {transactionId}
          </Text>
          <DepositStatusBadge status={status} />
        </div>
        <Text size="1" color="gray" weight="light" highContrast>
          Portfolio ID: {userPublicId}
        </Text>
      </div>
      {isPending && (
        <div className="flex gap-3">
          <Button
            size="1"
            variant="soft"
            radius="full"
            highContrast
            onClick={handleReject}
          >
            Reject
          </Button>
          <Button
            size="1"
            variant="soft"
            radius="full"
            color="gray"
            highContrast
            onClick={handleApprove}
          >
            Approve
          </Button>
        </div>
      )}
    </div>
  );
};

const renderBasicInfo = (data: DepositRecord) => {
  if (data.depositType === "FIAT") {
    const { amount, currency, senderAccount, createdAt } = data;
    return (
      <div className="grid grid-cols-4 gap-y-4 gap-x-6">
        <DetailRow label="Deposit method" value="Bank transfer" />
        <DetailRow label="Bank name" value={senderAccount?.bankName || "-"} />
        <DetailRow
          label="Bank account number"
          value={senderAccount?.accountNumber || "-"}
          enableCopy={!!senderAccount?.accountNumber}
          copyText={senderAccount?.accountNumber}
        />
        <DetailRow
          label="Deposit amount"
          value={
            amount ? `${amount.toLocaleString()} ${currency || ""}`.trim() : "-"
          }
        />
        <DetailRow label="Deposit date" value={formatDate(createdAt)} />
      </div>
    );
  }

  if (data.depositType === "CRYPTO") {
    const {
      amount,
      currency,
      transactionHash,
      fromAddress,
      network,
      createdAt,
    } = data;
    return (
      <div className="grid grid-cols-2 gap-y-4 gap-x-6">
        <DetailRow label="Deposit method" value="Blockchain transfer" />
        <DetailRow label="Transaction hash" value={transactionHash || "-"} />
        <DetailRow label="Wallet address" value={fromAddress || "-"} />
        <DetailRow label="Network" value={network || "-"} />
        <DetailRow label="Deposit date" value={formatDate(createdAt)} />
        <DetailRow
          label="Deposit amount"
          value={
            amount ? `${amount.toLocaleString()} ${currency || ""}`.trim() : "-"
          }
        />
      </div>
    );
  }
};

const renderFiles = (data: DepositRecord) => {
  if (data.depositType === "FIAT" && data.proofOfTransferFileKey) {
    return (
      <>
        <Separator size="4" />
        <div className="flex flex-col gap-2 items-start">
          <Text size="3" weight="medium">
            Submitted documents
          </Text>
          <Text size="2" color="gray">
            Uploaded documents
          </Text>
          <MediaButton
            title="Proof of transfer"
            url={data.proofOfTransferFileKey}
            type="image"
          />
        </div>
      </>
    );
  }
};

export const DepositDetails = ({ data }: { data: DepositRecord }) => {
  const { status, createdAt, operatorAuditDetails } = data;

  return (
    <div className="space-y-8">
      {/* Basic Information */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Basic information
        </Text>
        {renderBasicInfo(data)}
      </div>

      {renderFiles(data)}
      <Separator size="4" />

      {/* Audit Log */}
      <div className="flex flex-col gap-2">
        <Text size="3" weight="medium">
          Audit log
        </Text>

        {operatorAuditDetails ? (
          <Table.Root>
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Audit reason</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Audit user</Table.ColumnHeaderCell>
                <Table.ColumnHeaderCell>Audit time</Table.ColumnHeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              <Table.Row>
                <Table.Cell>
                  <DepositStatusBadge status={status} />
                </Table.Cell>
                <Table.Cell>
                  {operatorAuditDetails.reasonDescription ||
                    (status === "APPROVED" ? "-" : "Below minimum funds")}
                </Table.Cell>
                <Table.Cell>
                  {operatorAuditDetails.operatorName || "{user}"}
                </Table.Cell>
                <Table.Cell>{formatDate(createdAt)}</Table.Cell>
              </Table.Row>
            </Table.Body>
          </Table.Root>
        ) : (
          <div className="bg-[#00000008] rounded-lg">
            <InfoLayout
              className="py-10 px-6"
              title="No records found"
              icon="/empty-file.png"
              iconAlt="no data"
            />
          </div>
        )}
      </div>
    </div>
  );
};
